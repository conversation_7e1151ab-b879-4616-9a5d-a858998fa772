'use client';

import { renderFormFields } from '@/common/renderFormFields';
import { ButtonGradient } from '@/components/Buttons';
import { ItemBox } from '@/components/ItemBox';
import { useGetAvailableVersions } from '@/hooks/useGetAvailableVersions';
import { useNewVerifier } from '@/hooks/useNewVerifier';
import { DeploymentType } from '@/types/deployments';
import { NewVerifierFormInputsTypes } from '@/validation/newVerifierValidation';
import { useTranslations } from 'next-intl';
import { useGetDeploymentCreditsLeft } from '@/hooks/useGetDeploymentCreditsLeft';
import { LoaderSpinnerSmall } from '@/components/Loaders';
import { useMemo } from 'react';

const LOCALE_PREFIX = 'new_verifier';
const NETWORK_PREFIX = {
    1: '-testnet',
    2: '-mainnet',
};

const NewVerifier = () => {
    const t = useTranslations(LOCALE_PREFIX);
    const { versions } = useGetAvailableVersions({
        type: DeploymentType.VERIFIER,
    });

    const defaultValues = useMemo(
        () => ({
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            versionId: versions[0]?.value as any,
        }),
        [versions]
    );

    const {
        trigger,
        formFields,
        watchBlockchainConfigurationId,
        register,
        handleSubmit,
        errors,
        isValid,
        isSubmitting,
        onSubmit,
        control,
    } = useNewVerifier({
        defaultValues,
    });

    const { decoratedConfigs, isDeploymentAvailable } = useGetDeploymentCreditsLeft({ type: DeploymentType.VERIFIER });

    if (versions.length === 0) {
        return (
            <div className="px-10 h-full flex justify-center items-center">
                <div className="w-full max-w-xl flex justify-center items-center">
                    <LoaderSpinnerSmall />
                </div>
            </div>
        );
    }

    const fieldsToRender = renderFormFields({
        formFields,
        register,
        errors,
        t,
        formInputsTypes: NewVerifierFormInputsTypes,
        trigger,
        control,
        postfix: {
            verifierName: !watchBlockchainConfigurationId
                ? '-verifier'
                : `${NETWORK_PREFIX[watchBlockchainConfigurationId as keyof typeof NETWORK_PREFIX]}-verifier`,
        },
        defaultValues,
        selectOptions: {
            versionId: versions,
            blockchainConfigurationId: decoratedConfigs.map(item => item.configs),
        },
    });

    const renderForm = () => {
        return (
            <ItemBox>
                <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-4 p-6">
                    <h1 className="text-2xl text-left pb-4">{t('title')}</h1>
                    {fieldsToRender}
                    <ButtonGradient disabled={!isValid} style={{ marginTop: 24 }} isLoading={isSubmitting}>
                        {t('create_button')}
                    </ButtonGradient>
                </form>
            </ItemBox>
        );
    };

    return (
        <div className="px-10 h-full flex justify-center items-center">
            <div className="w-full max-w-xl">
                {isDeploymentAvailable ? (
                    renderForm()
                ) : (
                    <div className="opacity-50 pointer-events-none select-none">{renderForm()}</div>
                )}
            </div>
        </div>
    );
};

export default NewVerifier;
